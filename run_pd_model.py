#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np     
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

print("=== 开始运行PD模型 ===")

# 第一步：数据加载
print("\n1. 数据加载...")
df = pd.read_csv(r'loan_data_2007_2014.csv')
print(f"数据加载成功！数据形状: {df.shape}")
print(f"列数: {len(df.columns)}")

# 第二步：查看数据基本信息
print("\n2. 数据基本信息...")
print("数据信息:")
print(df.info())
print("\n前5行数据:")
print(df.head())

# 第三步：获取列名和目标变量
print("\n3. 获取列名和目标变量...")
df_feature = df.columns
print("所有列名:")
print(df_feature)
df_target = df['loan_status']
print(f"\n目标变量唯一值: {df_target.unique()}")

# 第四步：定义目标变量
print("\n4. 定义目标变量...")
bad_status = ['Charged Off','Default','Late (31-120 days)','Does not meet the credit policy. Status:Charged Off']
df['loan_status_new'] = df['loan_status'].apply(lambda x: 1 if x in bad_status else 0)
df = df.drop(columns='loan_status')
print("目标变量创建完成")
print(f"新目标变量分布:\n{df['loan_status_new'].value_counts()}")

# 第五步：处理缺失值
print("\n5. 处理缺失值...")
index_high_missing = []
for col in df.columns:
    missing_rate = df[col].isnull().sum() / len(df)
    if missing_rate >= 0.3:  # 修正条件
        index_high_missing.append(col)

print(f"高缺失率列 (>=30%): {index_high_missing}")

# 删除个人信息列和高缺失率列
index_personal = ['Unnamed: 0','id','member_id','emp_title','issue_d','url','desc','title']
index_drop = list(set(index_high_missing + index_personal))
df = df.drop(columns=index_drop)
print(f"删除列后数据形状: {df.shape}")

# 第六步：区分连续变量和离散变量
print("\n6. 区分连续变量和离散变量...")
discrete_columns = []
continuous_columns = []

for col in df.columns:
    if df[col].dtype in ['int64','float64']:
        unique_ratio = df[col].nunique() / len(df)
        if unique_ratio < 0.05:
            discrete_columns.append(col)
        else:
            continuous_columns.append(col)
    else:
        discrete_columns.append(col)

print(f'离散变量 ({len(discrete_columns)}个): {discrete_columns}')
print(f'连续变量 ({len(continuous_columns)}个): {continuous_columns}')

print("\n=== PD模型数据预处理完成 ===")
print(f"最终数据形状: {df.shape}")
print(f"目标变量分布:\n{df['loan_status_new'].value_counts()}")
print(f"违约率: {df['loan_status_new'].mean():.4f}")

# 第七步：创建离散变量DataFrame（修复原始代码中的错误）
print("\n7. 创建离散变量DataFrame...")
df_discrete = df[discrete_columns].copy()
df_continuous = df[continuous_columns].copy()
print(f"离散变量DataFrame形状: {df_discrete.shape}")
print(f"连续变量DataFrame形状: {df_continuous.shape}")

# 第八步：零膨胀二值化（修复原始代码中的错误）
print("\n8. 零膨胀二值化...")
col_zero_inflation = ['delinq_2yrs', 'inq_last_6mths', 'pub_rec', 'acc_now_delinq']
# 检查这些列是否存在
existing_cols = [col for col in col_zero_inflation if col in df.columns]
print(f"存在的零膨胀列: {existing_cols}")

# 先创建所有新的二值化列
for col in existing_cols:
    df[f'has_{col}'] = df[col].apply(lambda x: 1 if x > 0 else 0)
    print(f"创建了 has_{col} 列，唯一值: {df[f'has_{col}'].value_counts().to_dict()}")

# 然后一次性删除所有原始的零膨胀列
print(f"删除原始列: {existing_cols}")
df = df.drop(columns=existing_cols)

# 更新离散变量列表
discrete_columns = [col for col in discrete_columns if col not in existing_cols]
# 添加新创建的二值化列
for col in existing_cols:
    discrete_columns.append(f'has_{col}')

print(f"零膨胀处理后数据形状: {df.shape}")
print(f"更新后的离散变量数量: {len(discrete_columns)}")

# 第九步：进一步的数据探索
print("\n9. 数据探索...")
print(f"目标变量分布:")
print(df['loan_status_new'].value_counts())
print(f"违约率: {df['loan_status_new'].mean():.4f}")

# 查看新创建的二值化变量与目标变量的关系
print("\n新创建的二值化变量与违约率的关系:")
for col in [f'has_{c}' for c in existing_cols]:
    if col in df.columns:
        crosstab = pd.crosstab(df[col], df['loan_status_new'], normalize='index')
        print(f"{col}: 违约率 = {crosstab[1][1]:.4f} (有该特征时)")

print("\n=== 数据预处理全部完成 ===")
print(f"最终数据形状: {df.shape}")
print(f"离散变量数量: {len(discrete_columns)}")
print(f"连续变量数量: {len(continuous_columns)}")

# 保存处理后的数据
print("\n10. 保存处理后的数据...")
df.to_csv('processed_loan_data.csv', index=False)
print("数据已保存到 processed_loan_data.csv")

# 显示最终的列名
print(f"\n最终数据集包含的列:")
print(f"离散变量: {discrete_columns}")
print(f"连续变量: {continuous_columns}")
