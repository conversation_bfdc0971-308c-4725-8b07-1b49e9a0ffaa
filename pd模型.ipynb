{"cells": [{"cell_type": "code", "execution_count": 26, "id": "04b84833", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/5n/p46slwn530qgs58n0vqxs_q40000gn/T/ipykernel_13942/3951714780.py:6: DtypeWarning: Columns (20) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df=pd.read_csv(r'loan_data_2007_2014.csv')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 466285 entries, 0 to 466284\n", "Data columns (total 75 columns):\n", " #   Column                       Non-Null Count   Dtype  \n", "---  ------                       --------------   -----  \n", " 0   Unnamed: 0                   466285 non-null  int64  \n", " 1   id                           466285 non-null  int64  \n", " 2   member_id                    466285 non-null  int64  \n", " 3   loan_amnt                    466285 non-null  int64  \n", " 4   funded_amnt                  466285 non-null  int64  \n", " 5   funded_amnt_inv              466285 non-null  float64\n", " 6   term                         466285 non-null  object \n", " 7   int_rate                     466285 non-null  float64\n", " 8   installment                  466285 non-null  float64\n", " 9   grade                        466285 non-null  object \n", " 10  sub_grade                    466285 non-null  object \n", " 11  emp_title                    438697 non-null  object \n", " 12  emp_length                   445277 non-null  object \n", " 13  home_ownership               466285 non-null  object \n", " 14  annual_inc                   466281 non-null  float64\n", " 15  verification_status          466285 non-null  object \n", " 16  issue_d                      466285 non-null  object \n", " 17  loan_status                  466285 non-null  object \n", " 18  pymnt_plan                   466285 non-null  object \n", " 19  url                          466285 non-null  object \n", " 20  desc                         125981 non-null  object \n", " 21  purpose                      466285 non-null  object \n", " 22  title                        466264 non-null  object \n", " 23  zip_code                     466285 non-null  object \n", " 24  addr_state                   466285 non-null  object \n", " 25  dti                          466285 non-null  float64\n", " 26  delinq_2yrs                  466256 non-null  float64\n", " 27  earliest_cr_line             466256 non-null  object \n", " 28  inq_last_6mths               466256 non-null  float64\n", " 29  mths_since_last_delinq       215934 non-null  float64\n", " 30  mths_since_last_record       62638 non-null   float64\n", " 31  open_acc                     466256 non-null  float64\n", " 32  pub_rec                      466256 non-null  float64\n", " 33  revol_bal                    466285 non-null  int64  \n", " 34  revol_util                   465945 non-null  float64\n", " 35  total_acc                    466256 non-null  float64\n", " 36  initial_list_status          466285 non-null  object \n", " 37  out_prncp                    466285 non-null  float64\n", " 38  out_prncp_inv                466285 non-null  float64\n", " 39  total_pymnt                  466285 non-null  float64\n", " 40  total_pymnt_inv              466285 non-null  float64\n", " 41  total_rec_prncp              466285 non-null  float64\n", " 42  total_rec_int                466285 non-null  float64\n", " 43  total_rec_late_fee           466285 non-null  float64\n", " 44  recoveries                   466285 non-null  float64\n", " 45  collection_recovery_fee      466285 non-null  float64\n", " 46  last_pymnt_d                 465909 non-null  object \n", " 47  last_pymnt_amnt              466285 non-null  float64\n", " 48  next_pymnt_d                 239071 non-null  object \n", " 49  last_credit_pull_d           466243 non-null  object \n", " 50  collections_12_mths_ex_med   466140 non-null  float64\n", " 51  mths_since_last_major_derog  98974 non-null   float64\n", " 52  policy_code                  466285 non-null  int64  \n", " 53  application_type             466285 non-null  object \n", " 54  annual_inc_joint             0 non-null       float64\n", " 55  dti_joint                    0 non-null       float64\n", " 56  verification_status_joint    0 non-null       float64\n", " 57  acc_now_delinq               466256 non-null  float64\n", " 58  tot_coll_amt                 396009 non-null  float64\n", " 59  tot_cur_bal                  396009 non-null  float64\n", " 60  open_acc_6m                  0 non-null       float64\n", " 61  open_il_6m                   0 non-null       float64\n", " 62  open_il_12m                  0 non-null       float64\n", " 63  open_il_24m                  0 non-null       float64\n", " 64  mths_since_rcnt_il           0 non-null       float64\n", " 65  total_bal_il                 0 non-null       float64\n", " 66  il_util                      0 non-null       float64\n", " 67  open_rv_12m                  0 non-null       float64\n", " 68  open_rv_24m                  0 non-null       float64\n", " 69  max_bal_bc                   0 non-null       float64\n", " 70  all_util                     0 non-null       float64\n", " 71  total_rev_hi_lim             396009 non-null  float64\n", " 72  inq_fi                       0 non-null       float64\n", " 73  total_cu_tl                  0 non-null       float64\n", " 74  inq_last_12m                 0 non-null       float64\n", "dtypes: float64(46), int64(7), object(22)\n", "memory usage: 266.8+ MB\n", "None\n", "   Unnamed: 0       id  member_id  loan_amnt  funded_amnt  funded_amnt_inv  \\\n", "0           0  1077501    1296599       5000         5000           4975.0   \n", "1           1  1077430    1314167       2500         2500           2500.0   \n", "2           2  1077175    1313524       2400         2400           2400.0   \n", "3           3  1076863    1277178      10000        10000          10000.0   \n", "4           4  1075358    1311748       3000         3000           3000.0   \n", "\n", "         term  int_rate  installment grade  ... total_bal_il il_util  \\\n", "0   36 months     10.65       162.87     B  ...          NaN     NaN   \n", "1   60 months     15.27        59.83     C  ...          NaN     NaN   \n", "2   36 months     15.96        84.33     C  ...          NaN     NaN   \n", "3   36 months     13.49       339.31     C  ...          NaN     NaN   \n", "4   60 months     12.69        67.79     B  ...          NaN     NaN   \n", "\n", "  open_rv_12m open_rv_24m  max_bal_bc all_util total_rev_hi_lim inq_fi  \\\n", "0         NaN         NaN         NaN      NaN              NaN    NaN   \n", "1         NaN         NaN         NaN      NaN              NaN    NaN   \n", "2         NaN         NaN         NaN      NaN              NaN    NaN   \n", "3         NaN         NaN         NaN      NaN              NaN    NaN   \n", "4         NaN         NaN         NaN      NaN              NaN    NaN   \n", "\n", "  total_cu_tl inq_last_12m  \n", "0         NaN          NaN  \n", "1         NaN          NaN  \n", "2         NaN          NaN  \n", "3         NaN          NaN  \n", "4         NaN          NaN  \n", "\n", "[5 rows x 75 columns]\n"]}], "source": ["import pandas as pd\n", "import numpy as np     \n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "df=pd.read_csv(r'loan_data_2007_2014.csv')\n", "print(df.info(10))\n", "df.describe()\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 27, "id": "f784b13b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['Unnamed: 0', 'id', 'member_id', 'loan_amnt', 'funded_amnt',\n", "       'funded_amnt_inv', 'term', 'int_rate', 'installment', 'grade',\n", "       'sub_grade', 'emp_title', 'emp_length', 'home_ownership', 'annual_inc',\n", "       'verification_status', 'issue_d', 'loan_status', 'pymnt_plan', 'url',\n", "       'desc', 'purpose', 'title', 'zip_code', 'addr_state', 'dti',\n", "       'delinq_2yrs', 'earliest_cr_line', 'inq_last_6mths',\n", "       'mths_since_last_delinq', 'mths_since_last_record', 'open_acc',\n", "       'pub_rec', 'revol_bal', 'revol_util', 'total_acc',\n", "       'initial_list_status', 'out_prncp', 'out_prncp_inv', 'total_pymnt',\n", "       'total_pymnt_inv', 'total_rec_prncp', 'total_rec_int',\n", "       'total_rec_late_fee', 'recoveries', 'collection_recovery_fee',\n", "       'last_pymnt_d', 'last_pymnt_amnt', 'next_pymnt_d', 'last_credit_pull_d',\n", "       'collections_12_mths_ex_med', 'mths_since_last_major_derog',\n", "       'policy_code', 'application_type', 'annual_inc_joint', 'dti_joint',\n", "       'verification_status_joint', 'acc_now_delinq', 'tot_coll_amt',\n", "       'tot_cur_bal', 'open_acc_6m', 'open_il_6m', 'open_il_12m',\n", "       'open_il_24m', 'mths_since_rcnt_il', 'total_bal_il', 'il_util',\n", "       'open_rv_12m', 'open_rv_24m', 'max_bal_bc', 'all_util',\n", "       'total_rev_hi_lim', 'inq_fi', 'total_cu_tl', 'inq_last_12m'],\n", "      dtype='object')\n", "0          Fully Paid\n", "1         Charged Off\n", "2          <PERSON>y Paid\n", "3          <PERSON>y Paid\n", "4             Current\n", "             ...     \n", "466280        Current\n", "466281    Charged Off\n", "466282        Current\n", "466283     <PERSON><PERSON>\n", "466284        Current\n", "Name: loan_status, Length: 466285, dtype: object\n"]}], "source": ["#列名index\n", "df_feature=df.columns\n", "print(df_feature)\n", "df_target=df['loan_status']\n", "print(df_target)"]}, {"cell_type": "code", "execution_count": 28, "id": "fdb07a64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 466285 entries, 0 to 466284\n", "Data columns (total 75 columns):\n", " #   Column                       Non-Null Count   Dtype  \n", "---  ------                       --------------   -----  \n", " 0   Unnamed: 0                   466285 non-null  int64  \n", " 1   id                           466285 non-null  int64  \n", " 2   member_id                    466285 non-null  int64  \n", " 3   loan_amnt                    466285 non-null  int64  \n", " 4   funded_amnt                  466285 non-null  int64  \n", " 5   funded_amnt_inv              466285 non-null  float64\n", " 6   term                         466285 non-null  object \n", " 7   int_rate                     466285 non-null  float64\n", " 8   installment                  466285 non-null  float64\n", " 9   grade                        466285 non-null  object \n", " 10  sub_grade                    466285 non-null  object \n", " 11  emp_title                    438697 non-null  object \n", " 12  emp_length                   445277 non-null  object \n", " 13  home_ownership               466285 non-null  object \n", " 14  annual_inc                   466281 non-null  float64\n", " 15  verification_status          466285 non-null  object \n", " 16  issue_d                      466285 non-null  object \n", " 17  pymnt_plan                   466285 non-null  object \n", " 18  url                          466285 non-null  object \n", " 19  desc                         125981 non-null  object \n", " 20  purpose                      466285 non-null  object \n", " 21  title                        466264 non-null  object \n", " 22  zip_code                     466285 non-null  object \n", " 23  addr_state                   466285 non-null  object \n", " 24  dti                          466285 non-null  float64\n", " 25  delinq_2yrs                  466256 non-null  float64\n", " 26  earliest_cr_line             466256 non-null  object \n", " 27  inq_last_6mths               466256 non-null  float64\n", " 28  mths_since_last_delinq       215934 non-null  float64\n", " 29  mths_since_last_record       62638 non-null   float64\n", " 30  open_acc                     466256 non-null  float64\n", " 31  pub_rec                      466256 non-null  float64\n", " 32  revol_bal                    466285 non-null  int64  \n", " 33  revol_util                   465945 non-null  float64\n", " 34  total_acc                    466256 non-null  float64\n", " 35  initial_list_status          466285 non-null  object \n", " 36  out_prncp                    466285 non-null  float64\n", " 37  out_prncp_inv                466285 non-null  float64\n", " 38  total_pymnt                  466285 non-null  float64\n", " 39  total_pymnt_inv              466285 non-null  float64\n", " 40  total_rec_prncp              466285 non-null  float64\n", " 41  total_rec_int                466285 non-null  float64\n", " 42  total_rec_late_fee           466285 non-null  float64\n", " 43  recoveries                   466285 non-null  float64\n", " 44  collection_recovery_fee      466285 non-null  float64\n", " 45  last_pymnt_d                 465909 non-null  object \n", " 46  last_pymnt_amnt              466285 non-null  float64\n", " 47  next_pymnt_d                 239071 non-null  object \n", " 48  last_credit_pull_d           466243 non-null  object \n", " 49  collections_12_mths_ex_med   466140 non-null  float64\n", " 50  mths_since_last_major_derog  98974 non-null   float64\n", " 51  policy_code                  466285 non-null  int64  \n", " 52  application_type             466285 non-null  object \n", " 53  annual_inc_joint             0 non-null       float64\n", " 54  dti_joint                    0 non-null       float64\n", " 55  verification_status_joint    0 non-null       float64\n", " 56  acc_now_delinq               466256 non-null  float64\n", " 57  tot_coll_amt                 396009 non-null  float64\n", " 58  tot_cur_bal                  396009 non-null  float64\n", " 59  open_acc_6m                  0 non-null       float64\n", " 60  open_il_6m                   0 non-null       float64\n", " 61  open_il_12m                  0 non-null       float64\n", " 62  open_il_24m                  0 non-null       float64\n", " 63  mths_since_rcnt_il           0 non-null       float64\n", " 64  total_bal_il                 0 non-null       float64\n", " 65  il_util                      0 non-null       float64\n", " 66  open_rv_12m                  0 non-null       float64\n", " 67  open_rv_24m                  0 non-null       float64\n", " 68  max_bal_bc                   0 non-null       float64\n", " 69  all_util                     0 non-null       float64\n", " 70  total_rev_hi_lim             396009 non-null  float64\n", " 71  inq_fi                       0 non-null       float64\n", " 72  total_cu_tl                  0 non-null       float64\n", " 73  inq_last_12m                 0 non-null       float64\n", " 74  loan_status_new              466285 non-null  int64  \n", "dtypes: float64(46), int64(8), object(21)\n", "memory usage: 266.8+ MB\n", "None\n"]}], "source": ["#定义目标变量\n", "df_target=df['loan_status']\n", "bad_status=['Charged Off','Default','Late (31-120 days)','Does not meet the credit policy. Status:Charged Off']\n", "df['loan_status_new']=df['loan_status'].apply(lambda x:1 if x in bad_status else 0)\n", "clean_data=df.drop(columns='loan_status',inplace=True)\n", "print(df.info())"]}, {"cell_type": "code", "execution_count": 29, "id": "cb268e0a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['desc', 'mths_since_last_delinq', 'mths_since_last_record', 'next_pymnt_d', 'mths_since_last_major_derog', 'annual_inc_joint', 'dti_joint', 'verification_status_joint', 'open_acc_6m', 'open_il_6m', 'open_il_12m', 'open_il_24m', 'mths_since_rcnt_il', 'total_bal_il', 'il_util', 'open_rv_12m', 'open_rv_24m', 'max_bal_bc', 'all_util', 'inq_fi', 'total_cu_tl', 'inq_last_12m']\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 466285 entries, 0 to 466284\n", "Data columns (total 46 columns):\n", " #   Column                      Non-Null Count   Dtype  \n", "---  ------                      --------------   -----  \n", " 0   loan_amnt                   466285 non-null  int64  \n", " 1   funded_amnt                 466285 non-null  int64  \n", " 2   funded_amnt_inv             466285 non-null  float64\n", " 3   term                        466285 non-null  object \n", " 4   int_rate                    466285 non-null  float64\n", " 5   installment                 466285 non-null  float64\n", " 6   grade                       466285 non-null  object \n", " 7   sub_grade                   466285 non-null  object \n", " 8   emp_length                  445277 non-null  object \n", " 9   home_ownership              466285 non-null  object \n", " 10  annual_inc                  466281 non-null  float64\n", " 11  verification_status         466285 non-null  object \n", " 12  pymnt_plan                  466285 non-null  object \n", " 13  purpose                     466285 non-null  object \n", " 14  zip_code                    466285 non-null  object \n", " 15  addr_state                  466285 non-null  object \n", " 16  dti                         466285 non-null  float64\n", " 17  delinq_2yrs                 466256 non-null  float64\n", " 18  earliest_cr_line            466256 non-null  object \n", " 19  inq_last_6mths              466256 non-null  float64\n", " 20  open_acc                    466256 non-null  float64\n", " 21  pub_rec                     466256 non-null  float64\n", " 22  revol_bal                   466285 non-null  int64  \n", " 23  revol_util                  465945 non-null  float64\n", " 24  total_acc                   466256 non-null  float64\n", " 25  initial_list_status         466285 non-null  object \n", " 26  out_prncp                   466285 non-null  float64\n", " 27  out_prncp_inv               466285 non-null  float64\n", " 28  total_pymnt                 466285 non-null  float64\n", " 29  total_pymnt_inv             466285 non-null  float64\n", " 30  total_rec_prncp             466285 non-null  float64\n", " 31  total_rec_int               466285 non-null  float64\n", " 32  total_rec_late_fee          466285 non-null  float64\n", " 33  recoveries                  466285 non-null  float64\n", " 34  collection_recovery_fee     466285 non-null  float64\n", " 35  last_pymnt_d                465909 non-null  object \n", " 36  last_pymnt_amnt             466285 non-null  float64\n", " 37  last_credit_pull_d          466243 non-null  object \n", " 38  collections_12_mths_ex_med  466140 non-null  float64\n", " 39  policy_code                 466285 non-null  int64  \n", " 40  application_type            466285 non-null  object \n", " 41  acc_now_delinq              466256 non-null  float64\n", " 42  tot_coll_amt                396009 non-null  float64\n", " 43  tot_cur_bal                 396009 non-null  float64\n", " 44  total_rev_hi_lim            396009 non-null  float64\n", " 45  loan_status_new             466285 non-null  int64  \n", "dtypes: float64(26), int64(5), object(15)\n", "memory usage: 163.6+ MB\n", "None\n"]}], "source": ["#缺失值删去缺失率大于0.3和不具有区别意义的特征\n", "index_high_missing=[]\n", "for col in df.columns:\n", "   missing_rate=df[col].isnull().sum()/len(df)\n", "   if missing_rate<0.3:\n", "      continue\n", "   else:\n", "    index_high_missing.append(col)\n", "print(index_high_missing)\n", "df_high_missing=df[index_high_missing]\n", "index_personanl=['Unnamed: 0','id','member_id','emp_title','issue_d','url','desc','title']\n", "index_drop=set(index_high_missing+index_personanl)\n", "df=df.drop(columns=index_drop)\n", "print(df.info())\n"]}, {"cell_type": "code", "execution_count": null, "id": "80f4a839", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 30, "id": "1efcd498", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["discrete columns: ['loan_amnt', 'funded_amnt', 'funded_amnt_inv', 'term', 'int_rate', 'grade', 'sub_grade', 'emp_length', 'home_ownership', 'verification_status', 'pymnt_plan', 'purpose', 'zip_code', 'addr_state', 'dti', 'delinq_2yrs', 'earliest_cr_line', 'inq_last_6mths', 'open_acc', 'pub_rec', 'revol_util', 'total_acc', 'initial_list_status', 'total_rec_late_fee', 'recoveries', 'collection_recovery_fee', 'last_pymnt_d', 'last_credit_pull_d', 'collections_12_mths_ex_med', 'policy_code', 'application_type', 'acc_now_delinq', 'tot_coll_amt', 'total_rev_hi_lim', 'loan_status_new']\n", "continuous columns: ['installment', 'annual_inc', 'revol_bal', 'out_prncp', 'out_prncp_inv', 'total_pymnt', 'total_pymnt_inv', 'total_rec_prncp', 'total_rec_int', 'last_pymnt_amnt', 'tot_cur_bal']\n"]}], "source": ["#区别连续变量和离散变量\n", "discrete_columns=[]\n", "continuous_columns=[]\n", "for col in df.columns:\n", "   if df[col].dtype in ['int64','float64']:\n", "      unique_ratio=df[col].nunique()/len(df)\n", "      if unique_ratio<0.05:\n", "         discrete_columns.append(col)\n", "      else:\n", "         continuous_columns.append(col)\n", "   else:\n", "      discrete_columns.append(col)\n", "print('discrete columns:',discrete_columns)\n", "print('continuous columns:',continuous_columns)"]}, {"cell_type": "code", "execution_count": 31, "id": "e92b3e74", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x2000 with 20 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#离散变量可视化\n", "df_discrete=df[discrete_columns]\n", "df_discrete[df_discrete.columns[:-1]].hist(figsize=(20, 20),bins=30,sharex=False, sharey=False)\n", "plt.tight_layout()\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": 32, "id": "8d9bdc8b", "metadata": {}, "outputs": [], "source": ["#删去无意义特征\n", "df=df.drop(columns=['policy_code'],inplace=True)\n", "df_discrete=df_discrete.drop(columns=['policy_code'],inplace=True)\n"]}, {"cell_type": "code", "execution_count": 35, "id": "c30a9834", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'NoneType' object is not subscriptable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[35], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m col_zero_inflation\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdelinq_2yrs\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124minq_last_6mths\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpub_rec\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124macc_now_delinq\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m col \u001b[38;5;129;01min\u001b[39;00m col_zero_inflation:\n\u001b[0;32m----> 4\u001b[0m    df[\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhas_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mcol\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m=\u001b[39m\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[43mcol\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mapply(\u001b[38;5;28;01mlambda\u001b[39;00m x:\u001b[38;5;241m1\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m x\u001b[38;5;241m>\u001b[39m\u001b[38;5;241m0\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;241m0\u001b[39m)\n\u001b[1;32m      5\u001b[0m    df\u001b[38;5;241m=\u001b[39mdf\u001b[38;5;241m.\u001b[39mdrop(columns\u001b[38;5;241m=\u001b[39mcol_zero_inflation)\n\u001b[1;32m      6\u001b[0m    df_discrete\u001b[38;5;241m=\u001b[39mdf_discrete\u001b[38;5;241m.\u001b[39mdrop(columns\u001b[38;5;241m=\u001b[39mcol_zero_inflation)\n", "\u001b[0;31mTypeError\u001b[0m: 'NoneType' object is not subscriptable"]}], "source": ["#零膨胀二值化\n", "col_zero_inflation=['delinq_2yrs', 'inq_last_6mths', 'pub_rec', 'acc_now_delinq']\n", "for col in col_zero_inflation:\n", "   df[f'has_{col}']=df[col].apply(lambda x:1 if x>0 else 0)\n", "   df=df.drop(columns=col_zero_inflation)\n", "   df_discrete=df_discrete.drop(columns=col_zero_inflation)"]}, {"cell_type": "code", "execution_count": null, "id": "32d6a1c8", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'NoneType' object is not subscriptable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[30], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m#连续变量可视化\u001b[39;00m\n\u001b[0;32m----> 3\u001b[0m df_continuous\u001b[38;5;241m=\u001b[39m\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[43mcontinuous_columns\u001b[49m\u001b[43m]\u001b[49m\n\u001b[1;32m      4\u001b[0m fig,axes\u001b[38;5;241m=\u001b[39mplt\u001b[38;5;241m.\u001b[39msubplots(\n\u001b[1;32m      5\u001b[0m     \u001b[38;5;241m1\u001b[39m\u001b[38;5;241m+\u001b[39m\u001b[38;5;28mlen\u001b[39m(continuous_columns)\u001b[38;5;241m/\u001b[39m\u001b[38;5;241m/\u001b[39m\u001b[38;5;241m4\u001b[39m,\n\u001b[1;32m      6\u001b[0m     \u001b[38;5;241m4\u001b[39m,\n\u001b[1;32m      7\u001b[0m     figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m20\u001b[39m,\u001b[38;5;241m20\u001b[39m)\n\u001b[1;32m      8\u001b[0m )\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i ,feature \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(continuous_columns):\n", "\u001b[0;31mTypeError\u001b[0m: 'NoneType' object is not subscriptable"]}], "source": ["#连续变量可视化\n", "\n", "df_continuous=df[continuous_columns]\n", "fig,axes=plt.subplots(\n", "    1+len(continuous_columns)//4,\n", "    4,\n", "    figsize=(20,20)\n", ")\n", "for i ,feature in enumerate(continuous_columns):\n", "   row=i//4\n", "   col=i%4\n", "   ax=axes[row,col]\n", "   sns.histplot(df_continuous[feature],kde=True,ax=ax)\n", "   ax.set_title(feature)\n", "   ax.set_xticklabels([])\n", "plt.tight_layout()\n", "plt.show()  "]}, {"cell_type": "code", "execution_count": null, "id": "e5236429", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e35ae7cb", "metadata": {}, "outputs": [], "source": ["#删去零膨胀\n"]}, {"cell_type": "code", "execution_count": null, "id": "88dd7b5d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}