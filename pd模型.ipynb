import pandas as pd
import numpy as np     
import matplotlib.pyplot as plt
import seaborn as sns

df=pd.read_csv(r'loan_data_2007_2014.csv')
print(df.info(10))
df.describe()
print(df.head())


#列名index
df_feature=df.columns
print(df_feature)
df_target=df['loan_status']
print(df_target)

#定义目标变量
df_target=df['loan_status']
bad_status=['Charged Off','Default','Late (31-120 days)','Does not meet the credit policy. Status:Charged Off']
df['loan_status_new']=df['loan_status'].apply(lambda x:1 if x in bad_status else 0)
clean_data=df.drop(columns='loan_status',inplace=True)
print(df.info())

#缺失值删去缺失率大于0.3和不具有区别意义的特征
index_high_missing=[]
for col in df.columns:
   missing_rate=df[col].isnull().sum()/len(df)
   if missing_rate<0.3:
      continue
   else:
    index_high_missing.append(col)
print(index_high_missing)
df_high_missing=df[index_high_missing]
index_personanl=['Unnamed: 0','id','member_id','emp_title','issue_d','url','desc','title']
index_drop=set(index_high_missing+index_personanl)
df=df.drop(columns=index_drop)
print(df.info())




#区别连续变量和离散变量
discrete_columns=[]
continuous_columns=[]
for col in df.columns:
   if df[col].dtype in ['int64','float64']:
      unique_ratio=df[col].nunique()/len(df)
      if unique_ratio<0.05:
         discrete_columns.append(col)
      else:
         continuous_columns.append(col)
   else:
      discrete_columns.append(col)
print('discrete columns:',discrete_columns)
print('continuous columns:',continuous_columns)

#离散变量可视化
df_discrete=df[discrete_columns]
df_discrete[df_discrete.columns[:-1]].hist(figsize=(20, 20),bins=30,sharex=False, sharey=False)
plt.tight_layout()
plt.show()



#删去无意义特征
df=df.drop(columns=['policy_code'],inplace=True)
df_discrete=df_discrete.drop(columns=['policy_code'],inplace=True)


#零膨胀二值化
col_zero_inflation=['delinq_2yrs', 'inq_last_6mths', 'pub_rec', 'acc_now_delinq']
for col in col_zero_inflation:
   df[f'has_{col}']=df[col].apply(lambda x:1 if x>0 else 0)
   df=df.drop(columns=col_zero_inflation)
   df_discrete=df_discrete.drop(columns=col_zero_inflation)

#连续变量可视化

df_continuous=df[continuous_columns]
fig,axes=plt.subplots(
    1+len(continuous_columns)//4,
    4,
    figsize=(20,20)
)
for i ,feature in enumerate(continuous_columns):
   row=i//4
   col=i%4
   ax=axes[row,col]
   sns.histplot(df_continuous[feature],kde=True,ax=ax)
   ax.set_title(feature)
   ax.set_xticklabels([])
plt.tight_layout()
plt.show()  



#删去零膨胀


